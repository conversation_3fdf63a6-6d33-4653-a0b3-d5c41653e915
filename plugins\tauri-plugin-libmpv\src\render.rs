use glow::HasContext;
use glutin::config::{ConfigT<PERSON>plateBuilder, GlConfig};
use glutin::context::{ContextAttributesBuilder, NotCurrentGlContext};
use glutin::display::{DisplayApiPreference, GetGlDisplay, GlDisplay};
use glutin::surface::{GlSurface, WindowSurface};
use libmpv2::render::{OpenGLInitParams, RenderContext, RenderParam, RenderParamApiType};
use log::{error, info, trace, warn};
use raw_window_handle::{HasDisplayHandle, HasWindowHandle, RawWindowHandle};
use serde::de::DeserializeOwned;
use std::collections::HashMap;
use std::ffi::{c_void, CString};
use std::num::NonZeroU32;
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::thread;
use tauri::{plugin::<PERSON>lug<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Runtime};
use tauri::{Emitter, WebviewWindow};

pub fn setup_mpv_on_glutin(app: &tauri::App) {
    let window = app.get_webview_window("main").unwrap();

    thread::spawn(move || loop {
        while let Some(Ok(event)) = mpv.wait_event(0.0) {
            println!("[MPV EVENT] {:?}", event);
            if let libmpv2::events::Event::EndFile(_) = event {
                println!("[MPV DEBUG] End of file detected. Exiting render thread.");
                return;
            }
        }

        if redraw_flag.swap(false, std::sync::atomic::Ordering::SeqCst) {
            let size = window.inner_size().unwrap();
            println!(
                "[MPV DEBUG] Redrawing frame at size: {}x{}",
                size.width, size.height
            );

            render_context
                .render::<Arc<glutin::display::Display>>(0, size.width as _, size.height as _, true)
                .expect("Failed to draw video frame");

            gl_surface
                .swap_buffers(&current_context)
                .expect("Failed to swap buffers");
        }

        thread::sleep(std::time::Duration::from_millis(16));
    });
}
