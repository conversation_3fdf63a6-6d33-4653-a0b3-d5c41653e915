use glow::HasContext;
use glutin::config::{ConfigTemplateBuilder, GlConfig};
use glutin::context::{ContextAttributesBuilder, NotCurrentGlContext};
use glutin::display::{DisplayApiPreference, GetGlDisplay, GlDisplay};
use glutin::surface::WindowSurface;
use libmpv2::render::{OpenGLInitParams, RenderContext, RenderParam, RenderParamApiType};
use log::{error, info, trace, warn};
use raw_window_handle::{HasDisplayHandle, HasWindowHandle, RawWindowHandle};
use serde::de::DeserializeOwned;
use std::collections::HashMap;
use std::ffi::{c_void, CString};
use std::num::NonZeroU32;
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::thread;
use tauri::{plugin::Plugin<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Runtime};
use tauri::{Emitter, WebviewWindow};

use crate::error::mpv_error_code_to_name;
use crate::models::*;
use crate::{MpvExt, Result};

trait GlWindow {
    fn build_surface_attributes(
        &self,
        builder: glutin::surface::SurfaceAttributesBuilder<WindowSurface>,
    ) -> std::result::Result<
        glutin::surface::SurfaceAttributes<WindowSurface>,
        raw_window_handle::HandleError,
    >;
}

impl<R: Runtime> GlWindow for WebviewWindow<R> {
    fn build_surface_attributes(
        &self,
        builder: glutin::surface::SurfaceAttributesBuilder<WindowSurface>,
    ) -> std::result::Result<
        glutin::surface::SurfaceAttributes<WindowSurface>,
        raw_window_handle::HandleError,
    > {
        let (w, h) = self
            .inner_size()
            .unwrap()
            .non_zero()
            .expect("invalid zero inner size");
        let handle = self.window_handle()?.as_raw();
        Ok(builder.build(handle, w, h))
    }
}

trait NonZeroU32PhysicalSize {
    fn non_zero(self) -> Option<(NonZeroU32, NonZeroU32)>;
}

impl NonZeroU32PhysicalSize for winit::dpi::PhysicalSize<u32> {
    fn non_zero(self) -> Option<(NonZeroU32, NonZeroU32)> {
        let w = NonZeroU32::new(self.width)?;
        let h = NonZeroU32::new(self.height)?;
        Some((w, h))
    }
}

fn get_format_from_string(format_str: &str) -> Result<libmpv2::Format> {
    match format_str {
        "string" => Ok(libmpv2::Format::String),
        "flag" => Ok(libmpv2::Format::Flag),
        "int64" => Ok(libmpv2::Format::Int64),
        "double" => Ok(libmpv2::Format::Double),
        "node" => Ok(libmpv2::Format::Node),
        _ => Err(crate::Error::Format(format_str.to_string())),
    }
}

fn get_wid_from_handle(raw_handle: RawWindowHandle) -> Result<i64> {
    match raw_handle {
        RawWindowHandle::Win32(handle) => Ok(handle.hwnd.get() as i64),
        RawWindowHandle::Xlib(handle) => Ok(handle.window as i64),
        RawWindowHandle::AppKit(handle) => Ok(handle.ns_view.as_ptr() as i64),
        _ => Err(crate::Error::UnsupportedPlatform),
    }
}

fn get_proc_address(display: &Arc<glutin::display::Display>, name: &str) -> *mut c_void {
    match CString::new(name) {
        Ok(c_str) => display.get_proc_address(&c_str) as *mut _,
        Err(_) => std::ptr::null_mut(),
    }
}

pub fn init<R: Runtime, C: DeserializeOwned>(
    app: &AppHandle<R>,
    _api: PluginApi<R, C>,
) -> crate::Result<Mpv<R>> {
    info!("Plugin registered.");
    let mpv = Mpv {
        app: app.clone(),
        instances: Mutex::new(HashMap::new()),
    };
    Ok(mpv)
}

pub struct Mpv<R: Runtime> {
    app: AppHandle<R>,
    pub instances: Mutex<HashMap<String, MpvInstance>>,
}

impl<R: Runtime> Mpv<R> {
    pub fn init(&self, mpv_config: MpvConfig, window_label: &str) -> Result<String> {
        let app = self.app.clone();

        let mut instances_lock = app.mpv().instances.lock().unwrap();

        if instances_lock.contains_key(window_label) {
            info!(
                "mpv instance for window '{}' already exists. Skipping initialization.",
                window_label
            );
            return Ok(window_label.to_string());
        }

        let mut initial_options = mpv_config.initial_options.clone();

        let webview_window = app
            .get_webview_window(window_label)
            .ok_or(crate::Error::WindowNotFound(window_label.to_string()))?;

        match mpv_config.integration_mode {
            MpvIntegration::WindowId => {
                let window_handle = webview_window.window_handle()?;
                let raw_window_handle = window_handle.as_raw();
                let wid = get_wid_from_handle(raw_window_handle)?;

                initial_options.insert("wid".to_string(), serde_json::json!(wid));
            }
            MpvIntegration::RenderApi => {
                let window_handle = webview_window.window_handle()?;
                let raw_window_handle = window_handle.as_raw();
                let display_handle = webview_window.display_handle()?;
                let raw_display_handle = display_handle.as_raw();

                let gl_display = Arc::new(unsafe {
                    let preference = DisplayApiPreference::WglThenEgl(Some(raw_window_handle));
                    glutin::display::Display::new(raw_display_handle, preference)
                        .expect("Failed to create glutin display")
                });

                let surface_attributes = webview_window
                    .build_surface_attributes(Default::default())
                    .unwrap();
                let template =
                    ConfigTemplateBuilder::new().compatible_with_native_window(raw_window_handle);

                let gl_config = unsafe {
                    gl_display
                        .find_configs(template.build())
                        .unwrap()
                        .next()
                        .expect("No suitable config found")
                };

                let gl_surface = unsafe {
                    gl_display
                        .create_window_surface(&gl_config, &surface_attributes)
                        .expect("Failed to create window surface")
                };

                let context_attributes =
                    glutin::context::ContextAttributesBuilder::new().build(Some(raw_window_handle));
                let gl_context = unsafe {
                    gl_display
                        .create_context(&gl_config, &context_attributes)
                        .expect("Failed to create context")
                };

                let current_context = gl_context
                    .make_current(&gl_surface)
                    .expect("Failed to make context current");

                initial_options.insert("vo".to_string(), serde_json::json!("libmpv"));
            }
        }

        info!("Initializing mpv instance for window '{}'...", window_label);

        let mpv = libmpv2::Mpv::with_initializer(|init| {
            for (key, value) in initial_options {
                match value {
                    serde_json::Value::Bool(b) => init.set_option(&key, b)?,
                    serde_json::Value::Number(n) => {
                        if let Some(i) = n.as_i64() {
                            init.set_option(&key, i)?
                        } else if let Some(f) = n.as_f64() {
                            init.set_option(&key, f)?
                        }
                    }
                    serde_json::Value::String(s) => init.set_option(&key, s.as_str())?,
                    _ => {}
                }
            }

            Ok(())
        })
        .map_err(|e| crate::Error::Initialization(e.to_string()))?;

        if let MpvIntegration::RenderApi = mpv_config.integration_mode {
            let mut render_context = RenderContext::new(
                unsafe { mpv.ctx.as_mut() },
                vec![
                    RenderParam::ApiType(RenderParamApiType::OpenGl),
                    RenderParam::InitParams(OpenGLInitParams {
                        get_proc_address,
                        ctx: gl_display.clone(),
                    }),
                ],
            )
            .expect("Failed creating render context");

            let redraw_flag = Arc::new(AtomicBool::new(false));
            let redraw_flag_clone = redraw_flag.clone();
            render_context.set_update_callback(move || {
                redraw_flag_clone.store(true, std::sync::atomic::Ordering::SeqCst);
            });
            mpv.set_wakeup_callback(|| {});
        }

        let mut mpv_client = mpv
            .create_client(None)
            .map_err(|e| crate::Error::ClientCreation(e.to_string()))?;

        trace!(
            "Observing properties on init: {:?}",
            mpv_config.observed_properties
        );

        for (property, format_str) in mpv_config.observed_properties {
            let format = get_format_from_string(&format_str)?;
            trace!("Observing '{}' with format {:?}", property, format);
            if let Err(e) = mpv_client.observe_property(&property, format, 0) {
                error!("Failed to observe property '{}': {}", property, e);
                return Err(crate::Error::Mpv(e.to_string()));
            }
        }

        info!("mpv instance initialized for window '{}'.", window_label,);

        let instance = MpvInstance { mpv };
        instances_lock.insert(window_label.to_string(), instance);

        let app_handle = app.clone();

        let window_label_clone = window_label.to_string();

        std::thread::spawn(move || 'event_loop: loop {
            let event_result = mpv_client.wait_event(60.0);

            match event_result {
                Some(Ok(event)) => {
                    let raw_event_debug = format!("{:?}", event);
                    let serializable_event = SerializableMpvEvent::from(event);

                    let event_name = format!("mpv-event-{}", window_label_clone);

                    if let SerializableMpvEvent::Shutdown = serializable_event {
                        trace!(
                            "mpv event loop for window '{}' finished due to shutdown event.",
                            window_label_clone
                        );
                        let _ = app_handle.emit_to(
                            &window_label_clone,
                            &event_name,
                            &serializable_event,
                        );
                        break 'event_loop;
                    }

                    if let Err(e) =
                        app_handle.emit_to(&window_label_clone, &event_name, &serializable_event)
                    {
                        error!(
                            "Failed to emit mpv event to frontend: {}. Original event: {}",
                            e, raw_event_debug
                        );
                    }
                }
                None => continue 'event_loop,
                Some(Err(e)) => {
                    error!(
                        "Error in mpv event loop for window '{}': {}. Exiting.",
                        window_label_clone, e
                    );
                    break 'event_loop;
                }
            }
        });

        std::thread::spawn(move || 'render_loop: loop {
            if redraw_flag.swap(false, std::sync::atomic::Ordering::SeqCst) {
                let size = window.inner_size().unwrap();
                println!(
                    "[MPV DEBUG] Redrawing frame at size: {}x{}",
                    size.width, size.height
                );

                render_context
                    .render::<Arc<glutin::display::Display>>(
                        0,
                        size.width as _,
                        size.height as _,
                        true,
                    )
                    .expect("Failed to draw video frame");

                gl_surface
                    .swap_buffers(&current_context)
                    .expect("Failed to swap buffers");
            }

            thread::sleep(std::time::Duration::from_millis(16));
        });

        Ok(window_label.to_string())
    }

    pub fn destroy(&self, window_label: &str) -> Result<()> {
        let instance_to_kill = {
            let mut instances_lock = match self.app.mpv().instances.lock() {
                Ok(guard) => guard,
                Err(poisoned) => {
                    warn!("Mutex for mpv instances was poisoned. Recovering.");
                    poisoned.into_inner()
                }
            };
            instances_lock.remove(window_label)
        };

        if let Some(instance) = instance_to_kill {
            match instance.mpv.command("quit", &[]) {
                Ok(_) => {
                    info!(
                        "mpv instance for window '{}' destroyed successfully.",
                        window_label,
                    );
                    Ok(())
                }
                Err(e) => {
                    let error_message = format!(
                        "Failed to destroy mpv instance for window '{}': {}",
                        window_label, e,
                    );
                    error!("{}", error_message);
                    Err(crate::Error::Mpv(error_message))
                }
            }
        } else {
            info!(
            "No running mpv instance found for window '{}' to destroy. It may have already terminated.",
            window_label
        );
            Ok(())
        }
    }

    pub fn command(
        &self,
        name: &str,
        args: &Vec<serde_json::Value>,
        window_label: &str,
    ) -> Result<()> {
        if args.is_empty() {
            trace!("COMMAND '{}'", name);
        } else {
            trace!("COMMAND '{}' '{:?}'", name, args);
        }

        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let string_args: Vec<String> = args
                .iter()
                .map(|v| match v {
                    serde_json::Value::Bool(b) => {
                        if *b {
                            "yes".to_string()
                        } else {
                            "no".to_string()
                        }
                    }
                    serde_json::Value::Number(n) => n.to_string(),
                    serde_json::Value::String(s) => s.clone(),
                    _ => v.to_string().trim_matches('"').to_string(),
                })
                .collect();

            let args_as_slices: Vec<&str> = string_args.iter().map(|s| s.as_str()).collect();

            if let Err(e) = instance.mpv.command(name, &args_as_slices) {
                let error_details = match e {
                    libmpv2::Error::Raw(code) => {
                        format!("{} ({})", mpv_error_code_to_name(code), code)
                    }

                    _ => e.to_string(),
                };

                let error_message = format!(
                    "Failed to execute mpv command '{}' with args '{:?}': {}",
                    name, args, error_details
                );
                error!("{}", error_message);
                return Err(crate::Error::Command(error_message));
            }

            Ok(())
        } else {
            error!("mpv instance for window label '{}' not found", window_label);
            Ok(())
        }
    }

    pub fn set_property(
        &self,
        name: &str,
        value: &serde_json::Value,
        window_label: &str,
    ) -> crate::Result<()> {
        trace!("SET PROPERTY '{}' '{:?}'", name, value);

        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let _ = match value {
                serde_json::Value::Bool(b) => instance.mpv.set_property(name, *b),
                serde_json::Value::Number(n) => {
                    if let Some(i) = n.as_i64() {
                        instance.mpv.set_property(name, i)
                    } else if let Some(f) = n.as_f64() {
                        instance.mpv.set_property(name, f)
                    } else {
                        return Err(crate::Error::SetProperty(format!(
                            "Unsupported number format: {}",
                            n
                        )));
                    }
                }
                serde_json::Value::String(s) => instance.mpv.set_property(name, s.as_str()),
                serde_json::Value::Null => {
                    return Err(crate::Error::SetProperty(
                        "Cannot set property to null".to_string(),
                    ))
                }
                _ => {
                    return Err(crate::Error::SetProperty(format!(
                        "Unsupported value type for property '{}'",
                        name
                    )))
                }
            };

            Ok(())
        } else {
            Err(crate::Error::SetProperty(format!(
                "mpv instance for window label '{}' not found",
                window_label
            )))
        }
    }

    pub fn get_property(
        &self,
        name: String,
        format_str: Option<String>,
        window_label: &str,
    ) -> crate::Result<serde_json::Value> {
        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let result: std::result::Result<serde_json::Value, libmpv2::Error> = {
                if let Some(s) = format_str {
                    let format = get_format_from_string(&s)?;
                    match format {
                        libmpv2::Format::Flag => instance
                            .mpv
                            .get_property::<bool>(&name)
                            .map(serde_json::Value::from),
                        libmpv2::Format::Int64 => instance
                            .mpv
                            .get_property::<i64>(&name)
                            .map(serde_json::Value::from),
                        libmpv2::Format::Double => instance
                            .mpv
                            .get_property::<f64>(&name)
                            .map(serde_json::Value::from),
                        libmpv2::Format::String => instance
                            .mpv
                            .get_property::<String>(&name)
                            .map(serde_json::Value::from),
                        libmpv2::Format::Node => {
                            match instance.mpv.get_property::<MpvNode>(&name) {
                                Ok(wrapper) => Ok(wrapper.into_inner()),
                                Err(e) => Err(e),
                            }
                        }
                    }
                } else {
                    instance
                        .mpv
                        .get_property::<String>(&name)
                        .map(serde_json::Value::from)
                }
            };

            let value = match result {
                Ok(val) => val,
                Err(e) => return Err(e.into()),
            };

            trace!("GET PROPERTY '{}' '{:?}'", name, value);
            Ok(value)
        } else {
            Err(crate::Error::GetProperty(format!(
                "mpv instance for window label '{}' not found",
                window_label
            )))
        }
    }

    pub fn set_video_margin_ratio(
        &self,
        ratio: VideoMarginRatio,
        window_label: &str,
    ) -> Result<()> {
        trace!("SET VIDEO MARGIN RATIO '{:?}'", ratio);

        let app = self.app.clone();
        let instances_lock = match app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let mpv = &instance.mpv;
            if let Err(e) = mpv.set_property("video-margin-ratio-left", ratio.left.unwrap_or(0.0)) {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) = mpv.set_property("video-margin-ratio-right", ratio.right.unwrap_or(0.0))
            {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) = mpv.set_property("video-margin-ratio-top", ratio.top.unwrap_or(0.0)) {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) =
                mpv.set_property("video-margin-ratio-bottom", ratio.bottom.unwrap_or(0.0))
            {
                error!("Failed to set video margin ratio: {}", e);
            }
        }
        Ok(())
    }
}
